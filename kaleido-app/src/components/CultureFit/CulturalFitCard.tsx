import '../../styles/theming.css';

import { showToast } from '@/components/Toaster';
import { ICandidate, IJob } from '@/entities/interfaces';
import { apiClient } from '@/lib/apiHelper';
import { CheckCircle2, Clock, Eye, Mail, Video } from 'lucide-react';
import { useState } from 'react';

import VideoResponseModal from './VideoResponseModal';

// New StatusChip component
interface StatusChipProps {
  isCompleted: boolean;
}

const StatusChip = ({ isCompleted }: StatusChipProps) => {
  return (
    <span
      className="px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1.5"
      style={{
        backgroundColor: isCompleted ? 'var(--success-bg)' : 'var(--warning-bg)',
        color: isCompleted ? 'var(--success-color)' : 'var(--warning-color)',
        border: `1px solid ${isCompleted ? 'var(--success-color)' : 'var(--warning-color)'}`,
        opacity: 0.9,
      }}
    >
      {isCompleted ? <CheckCircle2 className="w-3 h-3" /> : <Clock className="w-3 h-3" />}
      {isCompleted ? 'Completed' : 'Pending'}
    </span>
  );
};

interface CulturalFitCardProps {
  candidate: ICandidate;
  questionLength: number;
  job?: IJob;
  onVideoModalOpen?: () => void;
  onVideoModalClose?: () => void;
}

const CulturalFitCard = ({
  candidate,
  questionLength,
  job,
  onVideoModalOpen,
  onVideoModalClose,
}: CulturalFitCardProps) => {
  const [showVideoResponses, setShowVideoResponses] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);

  // Calculate number of answered questions
  const answeredQuestions = candidate.videoResponses?.length || 0;
  // Get total questions from the first candidate that has responses
    // Determine video response status based on video responses
  const getVideoResponseStatus = () => {
    // If candidate has video responses, they've completed the interview
    if (candidate.videoResponses && candidate.videoResponses.length > 0) {
      return 'completed';
    }

    // Check if email has been sent using status.videoIntroEmailSent
    const hasVideoIntroEmailSent =
      candidate.status?.videoIntroEmailSent ||
      candidate.videoIntroEmailSent ||
      candidate.emailCorrespondence?.some(
        email =>
          (email.type === 'SENT' && email.subject.includes('Video Introduction')) ||
          email.metadata?.emailType === 'video_intro_invitation'
      );

    if (hasVideoIntroEmailSent) {
      return 'sent';
    }

    return 'not_sent';
  };

  const totalQuestions = questionLength;
    const videoResponseStatus = getVideoResponseStatus();

  const isCompleted =
    candidate.status?.videoIntroEmailSent ||
    (candidate.videoIntroEmailSent && videoResponseStatus === 'completed');




  // Check if candidate has email
  const hasEmail = candidate.email && candidate.email.trim() !== '';
  const isEmailDisabled = !hasEmail;

  // Get thresholds and determine score color
  const getScoreColor = (score: number) => {
    if (!job) {
      // Default colors when job info is not available
      if (score >= 80)
        return { bg: 'rgba(34, 197, 94, 0.1)', color: '#22c55e', border: 'rgba(34, 197, 94, 0.3)' };
      if (score >= 60)
        return {
          bg: 'rgba(251, 191, 36, 0.1)',
          color: '#fbbf24',
          border: 'rgba(251, 191, 36, 0.3)',
        };
      return { bg: 'rgba(239, 68, 68, 0.1)', color: '#ef4444', border: 'rgba(239, 68, 68, 0.3)' };
    }

    const topThreshold = parseFloat(job.topCandidateThreshold || '0');
    const secondTierThreshold = parseFloat(job.secondTierCandidateThreshold || '0');

    if (score >= topThreshold) {
      return { bg: 'rgba(34, 197, 94, 0.1)', color: '#22c55e', border: 'rgba(34, 197, 94, 0.3)' }; // Green - Top candidate
    } else if (score >= secondTierThreshold) {
      return { bg: 'rgba(251, 191, 36, 0.1)', color: '#fbbf24', border: 'rgba(251, 191, 36, 0.3)' }; // Yellow - Second tier
    } else {
      return { bg: 'rgba(239, 68, 68, 0.1)', color: '#ef4444', border: 'rgba(239, 68, 68, 0.3)' }; // Red - Below threshold
    }
  };

  const candidateScore = candidate.matchScore ?? candidate.evaluation?.matchScore ?? 0;
  const scoreColors = getScoreColor(candidateScore);

  // Handle sending video intro email
  const handleSendVideoIntroEmail = async () => {
    // Skip if email is disabled or already sending
    if (isEmailDisabled || isSendingEmail) {
      return;
    }

    if (!job?.id) {
      showToast({
        message: 'Job information is not available',
        type: 'error',
      });
      return;
    }

    setIsSendingEmail(true);
    try {
      const response = await apiClient.post('/email/invite-video-intro', {
        candidateEmail: candidate.email,
        candidateName: candidate.fullName,
        jobTitle: job.jobTitle || job.title || 'Position', // Try different job title properties
        jobId: job.id,
        candidateId: candidate.id,
      });

      if (response) {
        showToast({
          message:
            videoResponseStatus === 'sent'
              ? 'Video introduction email resent successfully!'
              : 'Video introduction email sent successfully!',
          type: 'success',
        });
      }
    } catch (error: any) {
      console.error('Failed to send video intro email:', error);
      const errorMessage = error.message || error.response?.data?.message || 'Failed to send email';

      showToast({
        message: errorMessage,
        type: 'error',
      });
    } finally {
      setIsSendingEmail(false);
    }
  };

  return (
    <div className="relative backdrop-blur-xl rounded-lg p-3 sm:p-4 hover:bg-white/10 transition-all duration-300 border border-white/5 hover:border-white/10 group">
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
        <div className="flex-1 min-w-0">
          <h3 className="font-medium truncate" style={{ color: 'var(--foreground-color)' }}>
            {candidate.fullName}
          </h3>
          <p style={{ color: 'var(--input-placeholder)' }} className="text-sm truncate">
            {candidate.jobTitle}
          </p>
          {candidate.email && (
            <p
              style={{ color: 'var(--input-placeholder)' }}
              className="text-xs mt-1 flex items-center gap-1 truncate"
            >
              <Mail className="w-3 h-3 flex-shrink-0" />
              <span className="truncate">{candidate.email}</span>
            </p>
          )}
        </div>
        <div className="flex flex-row sm:flex-col items-center sm:items-end gap-2">
          <StatusChip isCompleted={isCompleted} />
          {(candidate.matchScore !== undefined ||
            candidate.evaluation?.matchScore !== undefined) && (
            <div className="flex items-center gap-2">
              <div className="relative">
                <div
                  className="text-sm font-bold px-2 py-0.5 rounded-full"
                  style={{
                    backgroundColor: scoreColors.bg,
                    color: scoreColors.color,
                    border: `1px solid ${scoreColors.border}`,
                  }}
                >
                  {candidateScore}%
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="mt-3 sm:mt-4 relative">
        {videoResponseStatus === 'completed' ? (
          <>
            {/* Modern question indicator - positioned relative to the container */}
            <div className="absolute -top-2 right-0 bg-black/20 backdrop-blur-sm rounded-full px-2 py-0.5 border border-white/10 z-10 hidden sm:block">
              <span className="text-xs font-medium text-primary">
                {answeredQuestions} of {totalQuestions}
              </span>
            </div>
            {/* Show "View Response" button if completed */}
            <button
              type="button"
              onClick={() => {
                setShowVideoResponses(true);
                onVideoModalOpen?.();
              }}
              className="w-full group relative flex items-center justify-center gap-2 px-3 py-2 sm:px-4 sm:py-2.5 rounded-lg transition-all duration-300"
              style={{
                backgroundColor: 'var(--button-secondary-bg)',
                color: 'var(--button-secondary-text)',
              }}
            >
              <Eye className="w-4 h-4 transition-transform group-hover:scale-110" />
              <span className="font-medium text-sm sm:text-base">View Response</span>
            </button>
          </>
        ) : (
          <>
            {/* Show email sending button based on status */}
            <button
              type="button"
              onClick={isEmailDisabled ? undefined : handleSendVideoIntroEmail}
              disabled={isSendingEmail || isEmailDisabled}
              className={`w-full group relative flex items-center justify-center gap-2 px-3 py-2 sm:px-4 sm:py-2.5 rounded-lg transition-all duration-300 overflow-hidden ${
                isSendingEmail || isEmailDisabled ? 'cursor-not-allowed' : ''
              }`}
              style={{
                backgroundColor: isEmailDisabled
                  ? 'rgba(156, 163, 175, 0.1)' // Gray background for disabled
                  : videoResponseStatus === 'sent'
                    ? 'rgba(251, 191, 36, 0.1)' // Light amber background
                    : 'rgba(239, 68, 68, 0.15)', // Lighter red background
                backdropFilter: isEmailDisabled ? 'none' : 'blur(8px)',
                border: isEmailDisabled
                  ? '1px solid rgba(156, 163, 175, 0.2)'
                  : videoResponseStatus === 'sent'
                    ? '1px solid rgba(245, 158, 11, 0.3)'
                    : '1px solid rgba(239, 68, 68, 0.3)',
                color: isEmailDisabled
                  ? '#9ca3af' // Gray text for disabled
                  : videoResponseStatus === 'sent'
                    ? '#f59e0b' // Amber text
                    : '#ef4444', // Lighter red text
              }}
            >
              {isEmailDisabled ? (
                <>
                  <Mail className="w-4 h-4 opacity-50" />
                  <span className="font-medium text-sm sm:text-base">Email not available</span>
                </>
              ) : isSendingEmail ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span className="font-medium text-sm sm:text-base">Sending...</span>
                </>
              ) : (
                <>
                  {videoResponseStatus === 'sent' ? (
                    <>
                      <Mail className="w-4 h-4 transition-transform group-hover:scale-110" />
                      <span className="font-medium text-sm sm:text-base">
                        Resend Video Intro Email
                      </span>
                    </>
                  ) : (
                    <>
                      <Video className="w-4 h-4 transition-transform group-hover:scale-110" />
                      <span className="font-medium text-sm sm:text-base">
                        Send Video Intro Email
                      </span>
                    </>
                  )}
                </>
              )}
            </button>

            {/* Show email status indicator as small text below button */}
            {videoResponseStatus === 'sent' && !isEmailDisabled && (
              <div className="mt-2 text-center">
                <p className="text-xs text-amber-400">
                  • Video intro email sent - awaiting response
                </p>
              </div>
            )}
          </>
        )}
      </div>

      {showVideoResponses && (
        <VideoResponseModal
          candidate={candidate}
          onClose={() => {
            setShowVideoResponses(false);
            onVideoModalClose?.();
          }}
        />
      )}
    </div>
  );
};

export default CulturalFitCard;
