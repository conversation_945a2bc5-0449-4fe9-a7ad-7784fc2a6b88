'use client';

import { NavigationButton } from '@/components/common/NavigationButton';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { showToast } from '@/components/Toaster';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useSocialConnectors } from '@/stores/socialConnectorStore';
import { motion } from 'framer-motion';
import {
  Briefcase,
  ChevronDown,
  ClipboardList,
  Facebook,
  FileText,
  HeartHandshake,
  Instagram,
  Linkedin,
  MessageSquare,
  MoreVertical,
  Rocket,
  Settings,
  Twitter,
  Video,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import PublishDropdown, { PublishPlatform } from './PublishDropdown';

// Import tab components
import { JDToneTab } from './JobInfo/tabs/JDToneTab';
import { JobDetailsTab } from './JobInfo/tabs/JobDetailsTab';
import { VideoIntroQuestionsTab } from './JobInfo/tabs/VideoIntroQuestionsTab';
import { VideoJDTab } from './JobInfo/tabs/VideoJDTab';
import { CandidateStats } from './JobInfo/CandidateStats';

interface UnifiedJobViewProps {
  jobId: string;
  onClose?: () => void;
  embedded?: boolean; // When true, removes outer container styling
}

type TabType = 'job-details' | 'video-jd' | 'jd-tone' | 'video-intro';

export const UnifiedJobView: React.FC<UnifiedJobViewProps> = ({
  jobId,
  onClose,
  embedded = false,
}) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('job-details');
  const [isLoading, setIsLoading] = useState(true);
  const [isActionsMenuOpen, setIsActionsMenuOpen] = useState(false);
  const [publishDropdownOpen, setPublishDropdownOpen] = useState(false);
  const publishDropdownRef = useRef<HTMLDivElement>(null);
  const { setSelectedJob, selectedJob, fetchJobsByStatus } = useJobsStore();
  const {
    publishJob,
    unpublishJob,
    fetchJobDetails,
    currentJobDetails,
    selectedPlatforms,
    isPublishing,
    isUnpublishing,
    togglePlatformSelection,
  } = useJobStore();
  const { connectors: connectorStatuses, fetchConnectorStatuses } = useSocialConnectors();

  // Use currentJobDetails from store directly
  const job = currentJobDetails;

  // Define platforms for the publish dropdown
  const platforms: PublishPlatform[] = [
    {
      id: 'jobboard',
      name: 'Our Job Board',
      icon: <Briefcase className="w-4 h-4" />,
      color: '#8B5CF6',
      disabled: false,
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: <Linkedin className="w-4 h-4" />,
      color: '#0A66C2',
      disabled: !connectorStatuses.some(c => c.platform === 'linkedin' && c.isConnected),
    },
    {
      id: 'twitter',
      name: 'Twitter/X',
      icon: <Twitter className="w-4 h-4" />,
      color: '#1DA1F2',
      disabled: !connectorStatuses.some(c => c.platform === 'twitter' && c.isConnected),
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: <Facebook className="w-4 h-4" />,
      color: '#1877F2',
      disabled: !connectorStatuses.some(c => c.platform === 'facebook' && c.isConnected),
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: <Instagram className="w-4 h-4" />,
      color: '#E4405F',
      disabled: !connectorStatuses.some(c => c.platform === 'instagram' && c.isConnected),
    },
  ];

  const tabs = [
    { id: 'job-details' as const, label: 'Job Details', icon: FileText },
    { id: 'jd-tone' as const, label: 'JD Tone', icon: MessageSquare },
    { id: 'video-jd' as const, label: 'Video JD', icon: Video },
    { id: 'video-intro' as const, label: 'Video Intro', icon: HeartHandshake },
  ];

  // Handle tab parameter from URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search);
      const tabParam = searchParams.get('tab') as TabType;
      if (tabParam && tabs.some(tab => tab.id === tabParam)) {
        setActiveTab(tabParam);
      }
    }
  }, []);

  // Fetch job details
  useEffect(() => {
    const loadJobDetails = async () => {
      if (!jobId) return;

      // Use the store's fetchJobDetails method which handles caching
      setIsLoading(true);
      try {
        await fetchJobDetails(jobId);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching job details:', error);
        showToast({
          message: 'Failed to load job details',
          isSuccess: false,
        });
        setIsLoading(false);
      }
    };

    loadJobDetails();
  }, [jobId, fetchJobDetails]); // Only depend on jobId and the fetch function

  // Handle click outside for dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        publishDropdownRef.current &&
        !publishDropdownRef.current.contains(event.target as Node)
      ) {
        setPublishDropdownOpen(false);
      }
    };

    if (publishDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [publishDropdownOpen]);

  // No need for this useEffect anymore since we're using currentJob directly

  // Update URL when tab changes
  const handleTabChange = (tabId: TabType) => {
    setActiveTab(tabId);
    // Update URL without navigation
    const currentPath = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set('tab', tabId);
    const newUrl = `${currentPath}?${searchParams.toString()}`;
    window.history.replaceState(null, '', newUrl);
  };

  const handleEditClick = () => {
    // Navigate to job description creation flow in edit mode
    router.push(`/job-description-creation?jobId=${jobId}&step=basic-job-information&edit=true`);
  };

  const handleViewAssessment = () => {
    // Navigate to candidates page
    router.push(`/jobs/${jobId}/candidates`);
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      router.back();
    }
  };

  const handlePublish = async () => {
    if (!job || selectedPlatforms.length === 0) return;

    try {
      await publishJob(job.id, selectedPlatforms);
      showToast({
        message: `Job published to ${selectedPlatforms.length} platform${selectedPlatforms.length > 1 ? 's' : ''}`,
        isSuccess: true,
      });
      setPublishDropdownOpen(false);

      // Refresh job details
      await fetchJobDetails(job.id);
    } catch (error) {
      console.error('Error publishing job:', error);
      showToast({
        message: 'Failed to publish job',
        isSuccess: false,
      });
    }
  };

  const handleUnpublish = async () => {
    if (!job) return;

    try {
      await unpublishJob(job.id, ['jobboard']);
      showToast({
        message: 'Job unpublished successfully',
        isSuccess: true,
      });
      setPublishDropdownOpen(false);

      // Refresh job details
      await fetchJobDetails(job.id);
    } catch (error) {
      console.error('Error unpublishing job:', error);
      showToast({
        message: 'Failed to unpublish job',
        isSuccess: false,
      });
    }
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <ColorfulSmokeyOrbLoader text="Loading job details..." />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <p className="text-lg text-muted-foreground">Job not found</p>
          <button
            onClick={handleClose}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header with Tabs */}
      <div className="flex-none border-b border-gray-300/10 bg-background/95 h-[80px] relative">
        <div className="px-3 sm:px-6 h-full flex items-center justify-between gap-2">
          {/* Left side - Tab Navigation */}
          <div className="flex items-center h-full flex-1 min-w-0">
            {/* Tab Navigation - Always visible but responsive */}
            <div className="flex overflow-x-auto h-full [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
              {tabs.map((tab, index) => (
                <React.Fragment key={tab.id}>
                  {index > 0 && (
                    <div className="w-px bg-gray-300/10 self-center h-6 hidden sm:block" />
                  )}
                  <button
                    onClick={() => handleTabChange(tab.id)}
                    className={`
                    relative px-2 sm:px-4 h-full flex items-center gap-1 sm:gap-2 text-xs sm:text-sm font-medium transition-all whitespace-nowrap flex-shrink-0
                    ${
                      activeTab === tab.id
                        ? 'text-primary'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                    }
                  `}
                  >
                    {/* Gradient background for active tab */}
                    {activeTab === tab.id && (
                      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-600/10 to-pink-600/20" />
                    )}

                    {/* Content */}
                    <span className="relative z-10 flex items-center gap-1 sm:gap-2">
                      <tab.icon className="w-3.5 sm:w-4 h-3.5 sm:h-4" />
                      <span className="hidden sm:inline">{tab.label}</span>
                      <span className="sm:hidden">
                        {tab.label
                          .split(' ')
                          .map(word => word[0])
                          .join('')}
                      </span>
                    </span>

                    {/* Bottom border for active tab */}
                    {activeTab === tab.id && (
                      <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-600 to-pink-600" />
                    )}
                  </button>
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Right side - Action Buttons */}
          <div className="flex items-center gap-2 h-full">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center gap-4 h-full">
              {/* Vertical Separator */}
              <div className="h-8 w-px bg-gray-300/20" />

              <NavigationButton icon={ClipboardList} onClick={handleViewAssessment}>
                View Assessment
              </NavigationButton>

              {/* Vertical Separator */}
              <div className="h-8 w-px bg-gray-300/20" />

              <NavigationButton icon={Settings} onClick={handleEditClick}>
                Edit Job
              </NavigationButton>

              {/* Vertical Separator */}
              <div className="h-8 w-px bg-gray-300/20" />

              {/* Publish/Unpublish Dropdown */}
              <div className="relative" ref={publishDropdownRef}>
                <button
                  onClick={async () => {
                    // Only fetch connector statuses when opening the dropdown
                    if (!publishDropdownOpen && connectorStatuses.length === 0) {
                      await fetchConnectorStatuses();
                    }
                    setPublishDropdownOpen(!publishDropdownOpen);
                  }}
                  className="group relative flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ease-out disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    background: 'var(--button-primary-bg)',
                    color: 'var(--button-primary-text)',
                  }}
                >
                  <span className="font-medium">
                    {(job as any)?.isPublished ? 'Published' : 'Publish to'}
                  </span>
                  <ChevronDown
                    className={`w-4 h-4 transition-transform duration-200 ${publishDropdownOpen ? 'rotate-180' : ''}`}
                  />
                </button>

                {/* Publish dropdown */}
                <PublishDropdown
                  isOpen={publishDropdownOpen}
                  platforms={platforms}
                  selectedPlatforms={selectedPlatforms}
                  togglePlatformSelection={togglePlatformSelection}
                  handlePublish={handlePublish}
                  handleUnpublish={handleUnpublish}
                  isPublishing={isPublishing}
                  isUnpublishing={isUnpublishing}
                  isPublished={(job as any)?.isPublished}
                />
              </div>
            </div>

            {/* Mobile Actions Menu */}
            <div className="md:hidden relative">
              <button
                onClick={() => setIsActionsMenuOpen(!isActionsMenuOpen)}
                className="p-2 rounded-lg hover:bg-gray-800/20 transition-colors"
              >
                <MoreVertical className="w-5 h-5" />
              </button>

              {/* Actions Dropdown */}
              {isActionsMenuOpen && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  className="absolute right-0 top-full mt-2 w-48 bg-background/80 backdrop-blur-lg border border-gray-300/10 rounded-lg shadow-lg z-50"
                >
                  <div className="p-1">
                    <button
                      onClick={() => {
                        handleViewAssessment();
                        setIsActionsMenuOpen(false);
                      }}
                      className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm hover:bg-gray-800/20 transition-colors"
                    >
                      <ClipboardList className="w-4 h-4" />
                      View Assessment
                    </button>
                    <button
                      onClick={() => {
                        handleEditClick();
                        setIsActionsMenuOpen(false);
                      }}
                      className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm hover:bg-gray-800/20 transition-colors"
                    >
                      <Settings className="w-4 h-4" />
                      Edit Job
                    </button>
                    <div className="relative">
                      <button
                        onClick={async () => {
                          // Only fetch connector statuses when opening the dropdown
                          if (!publishDropdownOpen && connectorStatuses.length === 0) {
                            await fetchConnectorStatuses();
                          }
                          setPublishDropdownOpen(!publishDropdownOpen);
                          setIsActionsMenuOpen(false);
                        }}
                        className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm text-green-400 hover:bg-green-500/20 transition-colors"
                      >
                        <Rocket className="w-4 h-4" />
                        <span>{(job as any)?.isPublished ? 'Published' : 'Publish to'}</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto relative">
        <div className="relative min-h-full">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
            className="p-4 sm:p-6 lg:p-8 pb-24"
          >
            {activeTab === 'job-details' && job && <JobDetailsTab job={job} />}

            {activeTab === 'video-jd' && <VideoJDTab job={job} />}

            {activeTab === 'jd-tone' && <JDToneTab job={job} />}

            {activeTab === 'video-intro' && <VideoIntroQuestionsTab job={job} />}
          </motion.div>
          
        </div>
      </div>
    </div>
  );
};
