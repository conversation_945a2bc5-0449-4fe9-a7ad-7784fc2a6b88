import { <PERSON><PERSON><PERSON> } from '@/entities/interfaces';
import { Trophy, Medal, Users2 } from 'lucide-react';
import React from 'react';

interface CandidateStatsProps {
  job: IJob;
  className?: string;
}

export const CandidateStats: React.FC<CandidateStatsProps> = ({ job, className = '' }) => {
  // Total candidates - prioritize root level totalCandidates
  const totalCandidates = (job as any).totalCandidates || (job as any).stats?.totalCandidates || job.candidates?.length || 0;
  
  // Display the threshold values directly
  const topTierThreshold = job.topCandidateThreshold ? parseFloat(job.topCandidateThreshold) : null;
  const secondTierThreshold = job.secondTierCandidateThreshold ? parseFloat(job.secondTierCandidateThreshold) : null;

  return (
    <div className={`flex items-center justify-end gap-4 ${className}`}>
      <div className="flex items-center gap-4">
        {/* Total Candidates */}
        <div className="flex items-center gap-1.5">
          <Users2 className="w-4 h-4 text-white/70" />
          <div className="flex flex-col">
            <span className="text-sm font-semibold text-white">{totalCandidates}</span>
            <span className="text-[10px] text-white/50">Total</span>
          </div>
        </div>

        <div className="w-px h-7 bg-white/15" />

        {/* Top Tier */}
        <div className="flex items-center gap-1.5">
          <Trophy className="w-4 h-4 text-green-400" />
          <div className="flex flex-col">
            <span className="text-sm font-semibold text-white">{topTierThreshold ? `${topTierThreshold}%` : '-'}</span>
            <span className="text-[10px] text-white/50">Top Tier</span>
          </div>
        </div>

        <div className="w-px h-7 bg-white/15" />

        {/* Second Tier */}
        <div className="flex items-center gap-1.5">
          <Medal className="w-4 h-4 text-yellow-400" />
          <div className="flex flex-col">
            <span className="text-sm font-semibold text-white">{secondTierThreshold ? `${secondTierThreshold}%` : '-'}</span>
            <span className="text-[10px] text-white/50">Second Tier</span>
          </div>
        </div>
      </div>
    </div>
  );
};